<!-- 装修图文组件：谷歌地图 -->
<template>
  <view class="google-map-container" :style="containerStyle">
    <!-- 标题和全屏按钮同行 -->
    <view v-if="data.showTitle" class="map-header">
      <view class="map-title" :style="{ color: styles.titleColor }">
        {{ data.title }}
      </view>
      <button class="fullscreen-btn" @tap="openFullscreen">
        <text class="fullscreen-icon">⛶</text>
        <text class="fullscreen-text">全屏</text>
      </button>
    </view>
    <view v-if="data.showLocationDesc" class="map-desc" :style="{ color: styles.descColor }">
      {{ data.locationDesc }}
    </view>
    <view class="map-container" :style="{ height: data.height + 'px' }" id="googleMapRef"></view>

    <!-- 站点信息弹窗 -->
    <su-popup ref="stationPopup" type="center" :show="showStationPopup" @close="closeStationInfo">
      <view class="station-info-popup" v-if="selectedStation">
        <view class="popup-header">
          <text class="popup-title">站点详情</text>
          <text class="popup-close" @tap="closeStationInfo">×</text>
        </view>
        <view class="popup-content">
          <view class="station-name">{{ selectedStation.siteName }}</view>
          <view class="station-address">{{ selectedStation.address }}</view>
          <view class="station-status">
            <text class="status-label">状态：</text>
            <text :class="['status-value', getStatusClass(selectedStation.status)]">
              {{ getStatusText(selectedStation.status) }}
            </text>
          </view>
          <view class="station-distance" v-if="selectedStation.distance">
            <text class="distance-label">距离：</text>
            <text class="distance-value">{{ (selectedStation.distance / 1000).toFixed(1) }}km</text>
          </view>
          <view class="station-info" v-if="selectedStation.replaceCarCtn !== undefined">
            <text class="info-label">可换车辆：</text>
            <text class="info-value">{{ selectedStation.replaceCarCtn }}辆</text>
          </view>
          <view class="station-price" v-if="selectedStation.nowPrice">
            <text class="price-label">当前价格：</text>
            <text class="price-value">¥{{ selectedStation.nowPrice }}/度</text>
          </view>
        </view>
        <view class="popup-footer">
          <button class="popup-btn cancel-btn" @tap="closeStationInfo">关闭</button>
          <button class="popup-btn primary-btn" @tap="navigateToStation">导航</button>
        </view>
      </view>
    </su-popup>

    <!-- 全屏地图弹窗 -->
    <su-popup ref="fullscreenPopup" type="center" :show="showFullscreenMap" @close="closeFullscreen" round="0">
      <view class="fullscreen-map-popup">
        <view class="fullscreen-header">
          <text class="fullscreen-title">地图全览</text>
          <text class="fullscreen-close" @tap="closeFullscreen">×</text>
        </view>
        <view class="fullscreen-map-container" id="fullscreenMapRef"></view>
        <view class="fullscreen-footer">
          <button class="fullscreen-footer-btn" @tap="closeFullscreen">关闭</button>
        </view>
      </view>
    </su-popup>
  </view>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import sheep from '@/sheep'
import SiteApi from '@/sheep/api/site/site'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  styles: {
    type: Object,
    default: () => ({}),
  },
})

const containerStyle = {
  backgroundColor: props.styles.bgColor,
  borderRadius: (props.styles.borderRadius || 0) + 'px',
}

let map = null
let marker = null
const stationPopup = ref(null)
const selectedStation = ref(null)
const showStationPopup = ref(false)
const stationMarkers = []

// 全屏地图相关
const fullscreenPopup = ref(null)
const showFullscreenMap = ref(false)
let fullscreenMap = null
const fullscreenStationMarkers = []

// 加载谷歌地图API
const loadGoogleMapAPI = () => {
  return new Promise((resolve, reject) => {
    if (typeof window.google !== 'undefined' && window.google.maps) {
      resolve()
      return
    }
    const script = document.createElement('script')
    script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyArla_C7JZ6kpEFq9ojEK4YRFg6h8uhoQA&libraries=marker&callback=initGoogleMap'
    script.async = true
    script.defer = true
    window.initGoogleMap = () => resolve()
    script.onerror = () => reject(new Error('Google Maps API加载失败'))
    document.head.appendChild(script)
  })
}

// 获取站点列表
const getStationList = async () => {
  try {
    const { code, data } = await SiteApi.getSiteList({
      longitude: props.data.currentLocation?.lng || props.data.lng,
      latitude: props.data.currentLocation?.lat || props.data.lat,
      keyWord: ''
    })

    if (code === 0 && data && data.length > 0) {
      // 转换后端数据格式为前端期望的格式
      return data.map(station => ({
        siteNo: station.siteNo,
        siteName: station.siteName,
        address: station.siteAdress || station.address,
        latitude: station.latitude || station.lat,
        longitude: station.longitude || station.lng,
        status: station.siteStatus || station.status,
        distance: station.distance,
        // 其他字段
        city: station.city,
        enabled: station.enabled,
        replaceCarCtn: station.replaceCarCtn,
        lowPrice: station.lowPrice,
        normalPrice: station.normalPrice,
        peakPrice: station.peakPrice,
        nowPrice: station.nowPrice
      }))
    }

    // 如果后端数据为空，返回示例数据用于演示
    console.log('后端数据为空，使用示例数据进行演示')
    return getDemoStationData()
  } catch (error) {
    console.error('获取站点列表失败:', error)
    // 发生错误时也返回示例数据
    console.log('API调用失败，使用示例数据进行演示')
    return getDemoStationData()
  }
}

// 获取示例站点数据
const getDemoStationData = () => {
  return [
    {
      siteNo: 'DEMO001',
      siteName: '示例换电站1',
      address: '上海市浦东新区张江高科技园区',
      latitude: 31.23,
      longitude: 121.5,
      status: 1,
      distance: 1200,
      city: '上海',
      enabled: true,
      replaceCarCtn: 15,
      lowPrice: 0.8,
      normalPrice: 1.2,
      peakPrice: 1.8,
      nowPrice: 1.2
    },
    {
      siteNo: 'DEMO002',
      siteName: '示例换电站2',
      address: '上海市徐汇区漕河泾开发区',
      latitude: 31.18,
      longitude: 121.43,
      status: 2,
      distance: 2500,
      city: '上海',
      enabled: true,
      replaceCarCtn: 8,
      lowPrice: 0.9,
      normalPrice: 1.3,
      peakPrice: 1.9,
      nowPrice: 1.3
    },
    {
      siteNo: 'DEMO003',
      siteName: '示例换电站3',
      address: '上海市黄浦区外滩金融中心',
      latitude: 31.24,
      longitude: 121.49,
      status: 0,
      distance: 3800,
      city: '上海',
      enabled: false,
      replaceCarCtn: 0,
      lowPrice: 0.7,
      normalPrice: 1.1,
      peakPrice: 1.7,
      nowPrice: 1.1
    },
    {
      siteNo: 'DEMO004',
      siteName: '示例换电站4',
      address: '上海市静安区南京西路商圈',
      latitude: 31.22,
      longitude: 121.46,
      status: 1,
      distance: 1800,
      city: '上海',
      enabled: true,
      replaceCarCtn: 12,
      lowPrice: 0.85,
      normalPrice: 1.25,
      peakPrice: 1.85,
      nowPrice: 1.25
    },
    {
      siteNo: 'DEMO005',
      siteName: '示例换电站5',
      address: '上海市长宁区虹桥商务区',
      latitude: 31.20,
      longitude: 121.40,
      status: 1,
      distance: 3200,
      city: '上海',
      enabled: true,
      replaceCarCtn: 20,
      lowPrice: 0.75,
      normalPrice: 1.15,
      peakPrice: 1.75,
      nowPrice: 1.15
    }
  ]
}

// 缩放级别与图标大小的映射关系
const ZOOM_ICON_SIZE_MAP = {
  1: { width: 16, height: 16 },
  2: { width: 18, height: 18 },
  3: { width: 20, height: 20 },
  4: { width: 22, height: 22 },
  5: { width: 24, height: 24 },
  6: { width: 26, height: 26 },
  7: { width: 28, height: 28 },
  8: { width: 30, height: 30 },
  9: { width: 32, height: 32 },
  10: { width: 34, height: 34 },
  11: { width: 36, height: 36 },
  12: { width: 38, height: 38 },
  13: { width: 40, height: 40 },
  14: { width: 42, height: 42 },
  15: { width: 44, height: 44 },
  16: { width: 46, height: 46 },
  17: { width: 48, height: 48 },
  18: { width: 50, height: 50 },
  19: { width: 52, height: 52 },
  20: { width: 54, height: 54 }
}

// 计算当前缩放级别下的图标大小
const calculateIconSize = (zoom, baseSize) => {
  const minZoom = 1
  const maxZoom = 20
  const clampedZoom = Math.max(minZoom, Math.min(maxZoom, zoom))

  // 获取缩放级别对应的尺寸
  const zoomSize = ZOOM_ICON_SIZE_MAP[clampedZoom] || ZOOM_ICON_SIZE_MAP[9]

  // 根据基础尺寸计算实际尺寸
  const scaleFactor = Math.min(zoomSize.width / 32, zoomSize.height / 32)

  return {
    width: Math.round(baseSize.width * scaleFactor),
    height: Math.round(baseSize.height * scaleFactor)
  }
}

// 更新所有站点标记的图标大小
const updateStationMarkersSize = () => {
  if (!map || stationMarkers.length === 0) return

  const currentZoom = map.getZoom()
  const baseIconSize = {
    width: props.data.stationIconSize?.width || 32,
    height: props.data.stationIconSize?.height || 32
  }

  const newSize = calculateIconSize(currentZoom, baseIconSize)

  stationMarkers.forEach(marker => {
    const currentIcon = marker.getIcon()
    if (currentIcon && typeof currentIcon === 'object') {
      // 更新图标大小
      const updatedIcon = {
        ...currentIcon,
        scaledSize: new window.google.maps.Size(newSize.width, newSize.height)
      }
      marker.setIcon(updatedIcon)
    }
  })
}

// 渲染站点标记
const renderStationMarkers = (stations) => {
  // 清除现有标记
  stationMarkers.forEach(marker => marker.setMap(null))
  stationMarkers.length = 0

  const currentZoom = map ? map.getZoom() : props.data.zoom
  const baseIconSize = {
    width: props.data.stationIconSize?.width || 32,
    height: props.data.stationIconSize?.height || 32
  }

  // 计算当前缩放级别下的图标大小
  const iconSize = calculateIconSize(currentZoom, baseIconSize)

  stations.forEach(station => {
    // 根据站点状态选择对应的图标
    let iconUrl = getValidIconUrl(props.data.stationIcons?.online, 'online')
    if (station.status === 0) {
      iconUrl = getValidIconUrl(props.data.stationIcons?.offline, 'offline')
    } else if (station.status === 2) {
      iconUrl = getValidIconUrl(props.data.stationIcons?.busy, 'busy')
    }

    // 创建自定义图标，使用动态计算的大小
    const iconAnchor = {
      x: props.data.stationIconAnchor?.x ?? (iconSize.width / 2),
      y: props.data.stationIconAnchor?.y ?? iconSize.height
    }

    const icon = {
      url: iconUrl,
      scaledSize: new window.google.maps.Size(iconSize.width, iconSize.height),
      anchor: new window.google.maps.Point(iconAnchor.x, iconAnchor.y)
    }

    const stationMarker = new window.google.maps.Marker({
      position: { lat: station.latitude, lng: station.longitude },
      map: map,
      title: station.siteName,
      icon: icon
    })

    // 添加点击事件
    stationMarker.addListener('click', () => {
      showStationInfo(station)
    })

    stationMarkers.push(stationMarker)
  })

  // 如果启用自动居中且有站点，调整地图视野
  if (props.data.autoCenter && stations.length > 0) {
    const bounds = new window.google.maps.LatLngBounds()
    stations.forEach(station => {
      bounds.extend({ lat: station.latitude, lng: station.longitude })
    })
    map.fitBounds(bounds)
  }
}

// 获取有效的图标URL
const getValidIconUrl = (customIconUrl, statusType) => {
  // 如果自定义图标URL存在且有效，使用自定义图标
  if (customIconUrl && isValidImageUrl(customIconUrl)) {
    return customIconUrl
  }

  // 否则使用默认图标
  const defaultIcons = {
    online: 'https://maps.google.com/mapfiles/ms/icons/green-dot.png',
    offline: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',
    busy: 'https://maps.google.com/mapfiles/ms/icons/yellow-dot.png'
  }

  return defaultIcons[statusType] || defaultIcons.online
}

// 验证图片URL是否有效
const isValidImageUrl = (url) => {
  if (!url || typeof url !== 'string') {
    return false
  }

  // 检查是否是有效的URL格式
  try {
    new URL(url)
  } catch {
    return false
  }

  // 检查是否是图片文件
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg']
  const lowerUrl = url.toLowerCase()
  return imageExtensions.some(ext => lowerUrl.includes(ext)) ||
      lowerUrl.includes('data:image/') ||
      lowerUrl.includes('blob:')
}

// 显示站点信息
const showStationInfo = (station) => {
  if (!props.data.showStationInfo) return

  selectedStation.value = station
  showStationPopup.value = true
}

// 关闭站点信息
const closeStationInfo = () => {
  showStationPopup.value = false
  selectedStation.value = null
}

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 0: return 'offline'
    case 1: return 'online'
    case 2: return 'busy'
    default: return 'offline'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 0: return '暂停营业'
    case 1: return '营业中'
    case 2: return '繁忙'
    default: return '未知状态'
  }
}

// 导航到站点
const navigateToStation = () => {
  if (!selectedStation.value) return

  const { latitude, longitude } = selectedStation.value
  // 使用uni-app的导航功能
  uni.openLocation({
    latitude: latitude,
    longitude: longitude,
    name: selectedStation.value.siteName,
    address: selectedStation.value.address,
    success: () => {
      closeStationInfo()
    }
  })
}

// 打开全屏地图
const openFullscreen = () => {
  showFullscreenMap.value = true
  // 延迟初始化全屏地图，确保DOM已渲染
  setTimeout(() => {
    initFullscreenMap()
  }, 300)
}

// 关闭全屏地图
const closeFullscreen = () => {
  showFullscreenMap.value = false
  // 清理全屏地图资源
  if (fullscreenMap) {
    fullscreenStationMarkers.forEach(marker => marker.setMap(null))
    fullscreenStationMarkers.length = 0
    fullscreenMap = null
  }
}

// 初始化全屏地图
const initFullscreenMap = () => {
  const fullscreenMapRef = document.getElementById('fullscreenMapRef')
  if (!fullscreenMapRef || !window.google?.maps) return

  fullscreenMap = new window.google.maps.Map(fullscreenMapRef, {
    center: map ? map.getCenter() : { lat: props.data.lat, lng: props.data.lng },
    zoom: map ? map.getZoom() : props.data.zoom,
    disableDefaultUI: false,
    zoomControl: true,
    streetViewControl: true,  // 启用街景控件
    mapTypeControl: true,     // 启用地图类型控件
    fullscreenControl: true,  // 在全屏模式中启用原生全屏
    rotateControl: true,      // 启用旋转控件
    scaleControl: true,       // 启用比例尺控件
    mapId: 'FULLSCREEN_MAP_ID'
  })

  // 如果启用换电站显示，加载站点数据到全屏地图
  if (props.data.showStations) {
    loadFullscreenStationMarkers()
  }
}

// 加载全屏地图的站点标记
const loadFullscreenStationMarkers = async () => {
  try {
    const stations = await getStationList()
    renderFullscreenStationMarkers(stations)
  } catch (error) {
    console.error('加载全屏地图站点数据失败:', error)
  }
}

// 渲染全屏地图站点标记
const renderFullscreenStationMarkers = (stations) => {
  // 清除现有标记
  fullscreenStationMarkers.forEach(marker => marker.setMap(null))
  fullscreenStationMarkers.length = 0

  const currentZoom = fullscreenMap ? fullscreenMap.getZoom() : props.data.zoom
  const baseIconSize = {
    width: props.data.stationIconSize?.width || 32,
    height: props.data.stationIconSize?.height || 32
  }

  // 计算当前缩放级别下的图标大小
  const iconSize = calculateIconSize(currentZoom, baseIconSize)

  stations.forEach(station => {
    // 根据站点状态选择对应的图标
    let iconUrl = getValidIconUrl(props.data.stationIcons?.online, 'online')
    if (station.status === 0) {
      iconUrl = getValidIconUrl(props.data.stationIcons?.offline, 'offline')
    } else if (station.status === 2) {
      iconUrl = getValidIconUrl(props.data.stationIcons?.busy, 'busy')
    }

    // 创建自定义图标
    const iconAnchor = {
      x: props.data.stationIconAnchor?.x ?? (iconSize.width / 2),
      y: props.data.stationIconAnchor?.y ?? iconSize.height
    }

    const icon = {
      url: iconUrl,
      scaledSize: new window.google.maps.Size(iconSize.width, iconSize.height),
      anchor: new window.google.maps.Point(iconAnchor.x, iconAnchor.y)
    }

    const stationMarker = new window.google.maps.Marker({
      position: { lat: station.latitude, lng: station.longitude },
      map: fullscreenMap,
      title: station.siteName,
      icon: icon
    })

    // 添加点击事件
    stationMarker.addListener('click', () => {
      showStationInfo(station)
    })

    fullscreenStationMarkers.push(stationMarker)
  })

  // 如果启用自动居中且有站点，调整地图视野
  if (props.data.autoCenter && stations.length > 0) {
    const bounds = new window.google.maps.LatLngBounds()
    stations.forEach(station => {
      bounds.extend({ lat: station.latitude, lng: station.longitude })
    })
    fullscreenMap.fitBounds(bounds)
  }
}

const initMap = () => {
  const mapRef = document.getElementById('googleMapRef')
  if (!mapRef || !window.google?.maps) return
  map = new window.google.maps.Map(mapRef, {
    center: { lat: props.data.lat, lng: props.data.lng },
    zoom: props.data.zoom,
    disableDefaultUI: false,
    zoomControl: true,
    streetViewControl: true,  // 启用街景控件
    mapTypeControl: true,     // 启用地图类型控件
    fullscreenControl: false, // 禁用原生全屏（使用自定义）
    rotateControl: true,      // 启用旋转控件
    scaleControl: true,       // 启用比例尺控件
    mapId: 'DEMO_MAP_ID'
  })

  // 移除默认标记，只显示换电站图标
  // marker = new window.google.maps.Marker({
  //   position: { lat: props.data.lat, lng: props.data.lng },
  //   map: map,
  //   title: props.data.locationDesc,
  //   icon: {
  //     url: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',
  //     scaledSize: new window.google.maps.Size(32, 32),
  //     anchor: new window.google.maps.Point(16, 32)
  //   }
  // })

  // 监听地图缩放事件
  map.addListener('zoom_changed', () => {
    updateStationMarkersSize()
  })

  // 如果启用换电站显示，加载站点数据
  if (props.data.showStations) {
    loadStationMarkers()
  }
}

// 加载站点标记
const loadStationMarkers = async () => {
  try {
    const stations = await getStationList()
    renderStationMarkers(stations)
  } catch (error) {
    console.error('加载站点数据失败:', error)
  }
}

const updateMap = () => {
  if (!map) return
  const latLng = { lat: props.data.lat, lng: props.data.lng }
  map.setCenter(latLng)
  map.setZoom(props.data.zoom)
  // 移除默认标记的更新
  // if (marker) {
  //   marker.setPosition(latLng)
  // }
}

watch(
    () => [props.data.lat, props.data.lng, props.data.zoom],
    () => {
      updateMap()
    }
)

// 监听换电站配置变化
watch(
    () => props.data.showStations,
    (newVal) => {
      if (newVal && map) {
        loadStationMarkers()
      } else {
        // 清除站点标记
        stationMarkers.forEach(marker => marker.setMap(null))
        stationMarkers.length = 0
      }
    }
)

// 监听站点图标配置变化
watch(
    () => [props.data.stationIcons, props.data.stationIconSize, props.data.stationIconAnchor],
    () => {
      if (props.data.showStations && map && stationMarkers.length > 0) {
        // 重新加载站点标记以应用新的图标配置
        loadStationMarkers()
      }
    },
    { deep: true }
)

onMounted(async () => {
  try {
    await loadGoogleMapAPI()
    initMap()
  } catch (e) {
    console.error('加载谷歌地图失败:', e)
  }
})
</script>

<style lang="scss" scoped>
.google-map-container {
  width: 100%;
  padding: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
}

// 标题和全屏按钮同行布局
.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  .map-title {
    font-size: 16px;
    font-weight: bold;
    margin: 0;
    flex: 1;
  }

  .fullscreen-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    color: #495057;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
    margin-left: 12px;

    &:active {
      background: #dee2e6;
      transform: translateY(1px);
    }

    .fullscreen-icon {
      font-size: 14px;
      font-weight: bold;
    }

    .fullscreen-text {
      font-size: 12px;
      font-weight: 500;
    }
  }
}

// 独立的标题样式（当没有按钮时）
.map-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}
.map-desc {
  font-size: 14px;
  margin-bottom: 12px;
}
.map-container {
  width: 100%;
  min-height: 200px;
  background-color: #f1f1f1;
}

// 站点信息弹窗样式
.station-info-popup {
  background-color: #fff;
  border-radius: 12px;
  width: 300px;
  max-width: 90vw;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #eee;

    .popup-title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }

    .popup-close {
      font-size: 24px;
      color: #999;
      padding: 4px;
      cursor: pointer;
    }
  }

  .popup-content {
    padding: 20px;

    .station-name {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-bottom: 8px;
    }

    .station-address {
      font-size: 14px;
      color: #666;
      margin-bottom: 12px;
      line-height: 1.4;
    }

    .station-status {
      margin-bottom: 8px;

      .status-label {
        font-size: 14px;
        color: #666;
      }

      .status-value {
        font-size: 14px;
        font-weight: bold;

        &.online {
          color: #67c23a;
        }

        &.offline {
          color: #f56c6c;
        }

        &.busy {
          color: #e6a23c;
        }
      }
    }

    .station-distance {
      .distance-label {
        font-size: 14px;
        color: #666;
      }

      .distance-value {
        font-size: 14px;
        color: #409eff;
        font-weight: bold;
      }
    }

    .station-info {
      margin-bottom: 8px;

      .info-label {
        font-size: 14px;
        color: #666;
      }

      .info-value {
        font-size: 14px;
        color: #333;
        font-weight: bold;
      }
    }

    .station-price {
      .price-label {
        font-size: 14px;
        color: #666;
      }

      .price-value {
        font-size: 14px;
        color: #f56c6c;
        font-weight: bold;
      }
    }
  }

  .popup-footer {
    display: flex;
    padding: 16px 20px;
    border-top: 1px solid #eee;
    gap: 12px;

    .popup-btn {
      flex: 1;
      height: 40px;
      border-radius: 6px;
      border: none;
      font-size: 14px;
      cursor: pointer;

      &.cancel-btn {
        background-color: #f5f5f5;
        color: #666;
      }

      &.primary-btn {
        background-color: #409eff;
        color: #fff;
      }
    }
  }
}

// 全屏地图弹窗样式
.fullscreen-map-popup {
  background-color: #fff;
  border-radius: 12px;
  width: 90vw;
  height: 80vh;
  max-width: 90vw;
  display: flex;
  flex-direction: column;

  .fullscreen-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #eee;

    .fullscreen-title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }

    .fullscreen-close {
      font-size: 24px;
      color: #999;
      padding: 4px;
      cursor: pointer;
    }
  }

  .fullscreen-map-container {
    flex: 1;
    width: 100%;
    background-color: #f1f1f1;
  }

  .fullscreen-footer {
    padding: 16px 20px;
    border-top: 1px solid #eee;

    .fullscreen-footer-btn {
      width: 100%;
      height: 40px;
      background-color: #409eff;
      color: #fff;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      cursor: pointer;

      &:active {
        background-color: #337ecc;
      }
    }
  }
}
</style>
